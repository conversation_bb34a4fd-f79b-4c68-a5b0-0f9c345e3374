#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to set up the reviews collection in PocketBase
 * Run this script after starting your PocketBase server
 *
 * Usage: node scripts/setup-reviews-collection.js
 */

import PocketBase from 'pocketbase';
import process from 'node:process';

// Try different PocketBase URLs
const POCKETBASE_URLS = [
  process.env.POCKETBASE_URL,
  process.env.PUBLIC_POCKETBASE_URL,
  'https://trodoorentals.pockethost.io',
  'http://localhost:8090'
].filter(Boolean);

async function setupReviewsCollection() {
  let pb = null;
  let connectedUrl = null;

  // Try to connect to PocketBase
  for (const url of POCKETBASE_URLS) {
    try {
      console.log(`🔗 Trying to connect to PocketBase at: ${url}`);
      pb = new PocketBase(url);

      // Test connection by trying to get health status
      await pb.health.check();
      connectedUrl = url;
      console.log(`✅ Successfully connected to PocketBase at: ${url}`);
      break;
    } catch (error) {
      console.log(`❌ Failed to connect to ${url}:`, error.message);
      continue;
    }
  }

  if (!pb || !connectedUrl) {
    console.error('❌ Could not connect to any PocketBase instance');
    console.log('\n💡 Make sure PocketBase is running and accessible');
    console.log('💡 Set POCKETBASE_URL or PUBLIC_POCKETBASE_URL environment variable');
    process.exit(1);
  }

  try {
    // Try to authenticate as admin
    const adminEmail = process.env.POCKETBASE_ADMIN_EMAIL;
    const adminPassword = process.env.POCKETBASE_ADMIN_PASSWORD;

    if (!adminEmail || !adminPassword) {
      console.log('\n⚠️  Admin credentials not provided in environment variables');
      console.log('💡 Set POCKETBASE_ADMIN_EMAIL and POCKETBASE_ADMIN_PASSWORD');
      console.log('💡 Or create an admin account in PocketBase admin panel first');
      console.log('\n🔧 Attempting to proceed without authentication...');
    } else {
      console.log('🔐 Authenticating as admin...');
      await pb.admins.authWithPassword(adminEmail, adminPassword);
      console.log('✅ Admin authentication successful');
    }

    // Check if reviews collection already exists
    try {
      await pb.collections.getOne('reviews');
      console.log('ℹ️  Reviews collection already exists');
      return;
    } catch (error) {
      console.log('📝 Creating reviews collection...');
    }

    // Create the reviews collection
    const reviewsCollection = await pb.collections.create({
      name: 'reviews',
      type: 'base',
      schema: [
        {
          name: 'booking',
          type: 'relation',
          required: true,
          options: {
            collectionId: 'bookings',
            cascadeDelete: false,
            minSelect: null,
            maxSelect: 1,
            displayFields: []
          }
        },
        {
          name: 'renter',
          type: 'relation',
          required: true,
          options: {
            collectionId: 'users',
            cascadeDelete: false,
            minSelect: null,
            maxSelect: 1,
            displayFields: []
          }
        },
        {
          name: 'venue',
          type: 'relation',
          required: true,
          options: {
            collectionId: 'venues',
            cascadeDelete: false,
            minSelect: null,
            maxSelect: 1,
            displayFields: []
          }
        },
        {
          name: 'rating',
          type: 'number',
          required: true,
          options: {
            min: 1,
            max: 5
          }
        },
        {
          name: 'comment',
          type: 'text',
          required: true,
          options: {
            min: 10,
            max: 1000
          }
        },
        {
          name: 'owner_response',
          type: 'text',
          required: false,
          options: {
            min: null,
            max: 500
          }
        }
      ],
      indexes: [
        'CREATE UNIQUE INDEX `idx_reviews_booking` ON `reviews` (`booking`)',
        'CREATE INDEX `idx_reviews_venue` ON `reviews` (`venue`)',
        'CREATE INDEX `idx_reviews_renter` ON `reviews` (`renter`)'
      ],
      listRule: '',  // Public read access
      viewRule: '',  // Public read access
      createRule: '@request.auth.id = @request.data.renter && @request.data.booking.status = "completed"',
      updateRule: '@request.auth.id = venue.owner && owner_response = ""',
      deleteRule: '@request.auth.id != ""'  // Only authenticated users (for admin moderation)
    });

    console.log('✅ Reviews collection created successfully');

    // Update venues collection to add average_rating and review_count fields
    console.log('📝 Updating venues collection with rating fields...');
    
    try {
      const venuesCollection = await pb.collections.getOne('venues');
      
      // Add new fields to the schema
      const updatedSchema = [...venuesCollection.schema];
      
      // Check if fields already exist
      const hasAverageRating = updatedSchema.some(field => field.name === 'average_rating');
      const hasReviewCount = updatedSchema.some(field => field.name === 'review_count');
      
      if (!hasAverageRating) {
        updatedSchema.push({
          name: 'average_rating',
          type: 'number',
          required: false,
          options: {
            min: 0,
            max: 5
          }
        });
      }
      
      if (!hasReviewCount) {
        updatedSchema.push({
          name: 'review_count',
          type: 'number',
          required: false,
          options: {
            min: 0
          }
        });
      }
      
      if (!hasAverageRating || !hasReviewCount) {
        await pb.collections.update(venuesCollection.id, {
          schema: updatedSchema
        });
        console.log('✅ Venues collection updated with rating fields');
      } else {
        console.log('ℹ️  Venues collection already has rating fields');
      }
      
    } catch (error) {
      console.error('❌ Failed to update venues collection:', error.message);
    }

    console.log('🎉 Setup completed successfully!');
    console.log('\n📋 Summary:');
    console.log('- Reviews collection created with proper schema and API rules');
    console.log('- Venues collection updated with average_rating and review_count fields');
    console.log('- Unique constraint on booking field prevents duplicate reviews');
    console.log('- Security rules ensure only completed booking renters can create reviews');
    console.log('- Venue owners can add one response per review');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the setup
setupReviewsCollection();
